 ช่วยสร้าง **แอป Todo Application** ในรูปแบบ **Progressive Web App (PWA)** โดยมีรายละเอียดดังนี้:

### เทคโนโลยีหลัก
- **Frontend:** React + Vite + Tailwind CSS
- **ฐานข้อมูลในเครื่อง:** IndexedDB (ใช้ **Dexie.js** ในการจัดการ)
- **PWA:** มี Service Worker + manifest.json
- **สถาปัตยกรรม:** แยกเป็น Component ชัดเจน, โค้ดสะอาด, ขยายต่อได้ง่าย

### ฟีเจอร์หลัก (MVP)
1. เพิ่ม / แก้ไข / ลบ งาน (Todo)
2. ทำเครื่องหมายว่างานเสร็จแล้ว / ยังไม่เสร็จ
3. เก็บข้อมูลงานถาวรใน IndexedDB ผ่าน Dexie.js
4. UI รองรับทุกอุปกรณ์ (Mobile-first) ด้วย Tailwind CSS
5. รองรับการทำงานแบบ PWA:
   - มี manifest.json (ชื่อแอป, ไอคอน, theme color)
   - ลงทะเบียน Service Worker ให้สามารถใช้งานแบบ Offline ได้

### ฟีเจอร์แนะนำเพิ่มเติม
- ค้นหาและกรองงาน (เช่น แสดงทั้งหมด / งานที่เสร็จแล้ว / งานที่ยังไม่เสร็จ)
- กำหนดความสำคัญของงาน (ต่ำ / ปานกลาง / สูง)
- กำหนดวันครบกำหนดงาน (Due Date) พร้อม Date Picker
- การแจ้งเตือน (Notification API ของ Browser) [ตัวเลือกเสริม]
- โหมด Dark Mode (Tailwind Dark Mode)
- หน้า Settings อย่างง่าย (เช่น ลบข้อมูลทั้งหมด, reset DB)

### สิ่งที่ต้องส่งมอบ
- โปรเจกต์ React ที่สร้างด้วย Vite พร้อมใช้งาน
- ไฟล์ manifest.json และ service-worker.js ตั้งค่าแล้ว
- การเชื่อมต่อ Dexie.js พร้อม Schema สำหรับ Todo
- UI ที่สวยงามและอ่านง่าย ใช้ Tailwind CSS
- README หรือคำอธิบายสั้น ๆ วิธีรันและ build โปรเจกต์

import TodoItem from './TodoItem'

const TodoList = ({ todos, loading, onUpdateTodo, onDeleteTodo, onToggleTodo }) => {
  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-300">กำลังโหลด...</span>
        </div>
      </div>
    )
  }

  if (todos.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8">
        <div className="text-center">
          <div className="text-6xl mb-4">📝</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            ไม่มีงานในรายการ
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            เริ่มต้นด้วยการเพิ่มงานใหม่ด้านบน
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      {todos.map(todo => (
        <TodoItem
          key={todo.id}
          todo={todo}
          onUpdate={onUpdateTodo}
          onDelete={onDeleteTodo}
          onToggle={onToggleTodo}
        />
      ))}
    </div>
  )
}

export default TodoList

import <PERSON>ie from 'dexie';

// Define the database schema
export class TodoDatabase extends <PERSON>ie {
  constructor() {
    super('TodoDatabase');
    
    this.version(1).stores({
      todos: '++id, title, description, completed, priority, dueDate, createdAt, updatedAt'
    });
  }
}

// Create database instance
export const db = new TodoDatabase();

// Todo CRUD operations
export const todoService = {
  // Create a new todo
  async addTodo(todo) {
    const now = new Date();
    const newTodo = {
      ...todo,
      completed: false,
      createdAt: now,
      updatedAt: now
    };
    return await db.todos.add(newTodo);
  },

  // Get all todos
  async getAllTodos() {
    return await db.todos.orderBy('createdAt').reverse().toArray();
  },

  // Get todo by ID
  async getTodoById(id) {
    return await db.todos.get(id);
  },

  // Update todo
  async updateTodo(id, updates) {
    const updatedTodo = {
      ...updates,
      updatedAt: new Date()
    };
    return await db.todos.update(id, updatedTodo);
  },

  // Delete todo
  async deleteTodo(id) {
    return await db.todos.delete(id);
  },

  // Toggle todo completion
  async toggleTodo(id) {
    const todo = await db.todos.get(id);
    if (todo) {
      return await db.todos.update(id, {
        completed: !todo.completed,
        updatedAt: new Date()
      });
    }
  },

  // Get todos by filter
  async getTodosByFilter(filter) {
    switch (filter) {
      case 'completed':
        return await db.todos.where('completed').equals(true).toArray();
      case 'pending':
        return await db.todos.where('completed').equals(false).toArray();
      case 'high':
        return await db.todos.where('priority').equals('high').toArray();
      case 'medium':
        return await db.todos.where('priority').equals('medium').toArray();
      case 'low':
        return await db.todos.where('priority').equals('low').toArray();
      default:
        return await this.getAllTodos();
    }
  },

  // Search todos by title or description
  async searchTodos(searchTerm) {
    const allTodos = await this.getAllTodos();
    return allTodos.filter(todo => 
      todo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (todo.description && todo.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  },

  // Clear all todos
  async clearAllTodos() {
    return await db.todos.clear();
  },

  // Get todos count by status
  async getTodosCount() {
    const total = await db.todos.count();
    const completed = await db.todos.where('completed').equals(true).count();
    const pending = await db.todos.where('completed').equals(false).count();
    
    return {
      total,
      completed,
      pending
    };
  }
};

# 📝 Todo PWA - แอปจัดการงานแบบ Progressive Web App

แอปพลิเคชันจัดการงานที่ทันสมัย สร้างด้วย React, Vite, Tailwind CSS และ IndexedDB พร้อมฟีเจอร์ PWA สำหรับการใช้งานแบบออฟไลน์

## ✨ ฟีเจอร์หลัก

### 🎯 ฟีเจอร์พื้นฐาน (MVP)
- ✅ เพิ่ม / แก้ไข / ลบ งาน (Todo)
- ✅ ทำเครื่องหมายว่างานเสร็จแล้ว / ยังไม่เสร็จ
- ✅ เก็บข้อมูลงานถาวรใน IndexedDB ผ่าน Dexie.js
- ✅ UI รองรับทุกอุปกรณ์ (Mobile-first) ด้วย Tailwind CSS
- ✅ รองรับการทำงานแบบ PWA (Progressive Web App)

### 🚀 ฟีเจอร์ขั้นสูง
- ✅ ค้นหาและกรองงาน (ทั้งหมด / เสร็จแล้ว / รอดำเนินการ / ตามความสำคัญ)
- ✅ กำหนดความสำคัญของงาน (ต่ำ / ปานกลาง / สูง)
- ✅ กำหนดวันครบกำหนดงาน (Due Date) พร้อม Date Picker
- ✅ โหมด Dark Mode (Tailwind Dark Mode)
- ✅ หน้า Settings (ลบข้อมูลทั้งหมด, toggle dark mode)
- ✅ แสดงสถิติงาน (ทั้งหมด / เสร็จแล้ว / รอดำเนินการ)
- ✅ แจ้งเตือนงานที่เลยกำหนด

## 🛠️ เทคโนโลยีที่ใช้

- **Frontend Framework:** React 19.1.1
- **Build Tool:** Vite 7.1.2
- **Styling:** Tailwind CSS 4.1.12
- **Database:** IndexedDB ผ่าน Dexie.js 4.2.0
- **PWA:** Service Worker + Web App Manifest
- **Language:** JavaScript (ES6+)

## 📦 การติดตั้งและรัน

### ข้อกำหนดระบบ
- Node.js เวอร์ชัน 20.19.0 หรือสูงกว่า
- npm หรือ yarn

### ขั้นตอนการติดตั้ง

1. **Clone หรือ Download โปรเจกต์**
```bash
git clone <repository-url>
cd TodoApp
```

2. **ติดตั้ง Dependencies**
```bash
npm install
```

3. **รันในโหมด Development**
```bash
npm run dev
```

4. **เปิดเบราว์เซอร์**
   - ไปที่ `http://localhost:5173`
   - แอปจะเปิดขึ้นมาพร้อมใช้งาน

## 🏗️ การ Build และ Deploy

### Build สำหรับ Production
```bash
npm run build
```
ไฟล์ที่ build แล้วจะอยู่ในโฟลเดอร์ `dist/`

### Preview Build
```bash
npm run preview
```

### Deploy
1. **Static Hosting (Netlify, Vercel, GitHub Pages)**
   - Upload โฟลเดอร์ `dist/` ไปยัง hosting service
   - ตั้งค่า redirect สำหรับ SPA (Single Page Application)

2. **Server Deployment**
   - Copy โฟลเดอร์ `dist/` ไปยัง web server
   - ตั้งค่า web server ให้ serve static files
   - ตั้งค่า HTTPS สำหรับ PWA features

## 📱 PWA Features

### การติดตั้งแอป
- เปิดแอปในเบราว์เซอร์
- คลิก "Add to Home Screen" หรือ "Install App"
- แอปจะติดตั้งเหมือน Native App

### การใช้งานแบบ Offline
- แอปสามารถใช้งานได้แม้ไม่มีอินเทอร์เน็ต
- ข้อมูลจะถูกเก็บใน IndexedDB ในเครื่อง
- Service Worker จะ cache ไฟล์สำคัญ

## 🗂️ โครงสร้างโปรเจกต์

```
TodoApp/
├── public/
│   ├── manifest.json          # PWA manifest
│   ├── sw.js                  # Service Worker
│   ├── icon-192x192.png       # PWA icons
│   ├── icon-512x512.png
│   └── apple-touch-icon.png
├── src/
│   ├── components/            # React Components
│   │   ├── Header.jsx         # หัวเรื่องและสถิติ
│   │   ├── AddTodo.jsx        # ฟอร์มเพิ่มงาน
│   │   ├── TodoList.jsx       # รายการงาน
│   │   ├── TodoItem.jsx       # รายการงานแต่ละรายการ
│   │   ├── SearchBar.jsx      # ช่องค้นหา
│   │   ├── FilterBar.jsx      # ปุ่มกรอง
│   │   └── Settings.jsx       # หน้าตั้งค่า
│   ├── db/
│   │   └── database.js        # Dexie.js database setup
│   ├── App.jsx                # Main App component
│   ├── main.jsx               # Entry point
│   └── index.css              # Tailwind CSS imports
├── index.html                 # HTML template
├── package.json               # Dependencies
├── tailwind.config.js         # Tailwind configuration
├── postcss.config.js          # PostCSS configuration
└── vite.config.js             # Vite configuration
```

## 🎨 การใช้งาน

### เพิ่มงานใหม่
1. คลิกในช่อง "เพิ่มงานใหม่..."
2. ใส่ชื่องาน (บังคับ)
3. เพิ่มรายละเอียด (ไม่บังคับ)
4. เลือกความสำคัญ
5. กำหนดวันครบกำหนด (ไม่บังคับ)
6. คลิก "เพิ่มงาน"

### จัดการงาน
- **ทำเครื่องหมายเสร็จ:** คลิกที่ checkbox
- **แก้ไข:** คลิกไอคอน ✏️
- **ลบ:** คลิกไอคอน 🗑️

### ค้นหาและกรอง
- **ค้นหา:** พิมพ์ในช่องค้นหา
- **กรอง:** คลิกปุ่มกรองตามสถานะหรือความสำคัญ

### ตั้งค่า
- **Dark Mode:** คลิกไอคอน 🌙/☀️
- **Settings:** คลิกไอคอน ⚙️

## 🔧 การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

1. **แอปไม่โหลด**
   - ตรวจสอบ Node.js version (ต้อง >= 20.19.0)
   - ลบ `node_modules` และ `package-lock.json` แล้ว `npm install` ใหม่

2. **PWA ไม่ทำงาน**
   - ตรวจสอบว่าใช้ HTTPS
   - ตรวจสอบ Service Worker ใน DevTools

3. **ข้อมูลหาย**
   - ข้อมูลเก็บใน IndexedDB ของเบราว์เซอร์
   - การล้าง browser data จะทำให้ข้อมูลหาย

## 📄 License

MIT License - ใช้งานได้อย่างอิสระ

## 🤝 การมีส่วนร่วม

หากต้องการปรับปรุงหรือเพิ่มฟีเจอร์:
1. Fork โปรเจกต์
2. สร้าง feature branch
3. Commit การเปลี่ยนแปลง
4. Push ไปยัง branch
5. สร้าง Pull Request

---

**สร้างด้วย ❤️ โดยใช้ React + Vite + Tailwind CSS**

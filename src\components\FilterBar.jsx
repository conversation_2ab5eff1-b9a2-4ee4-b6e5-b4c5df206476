const FilterBar = ({ currentFilter, onFilterChange }) => {
  const filters = [
    { value: 'all', label: 'ทั้งหมด', icon: '📋' },
    { value: 'pending', label: 'รอดำเนินการ', icon: '⏳' },
    { value: 'completed', label: 'เสร็จแล้ว', icon: '✅' },
    { value: 'high', label: 'สำคัญสูง', icon: '🔴' },
    { value: 'medium', label: 'สำคัญปานกลาง', icon: '🟡' },
    { value: 'low', label: 'สำคัญต่ำ', icon: '🟢' }
  ]

  return (
    <div className="flex flex-wrap gap-2">
      {filters.map(filter => (
        <button
          key={filter.value}
          onClick={() => onFilterChange(filter.value)}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2 ${
            currentFilter === filter.value
              ? 'bg-blue-600 text-white'
              : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600'
          }`}
        >
          <span>{filter.icon}</span>
          <span className="hidden sm:inline">{filter.label}</span>
        </button>
      ))}
    </div>
  )
}

export default FilterBar

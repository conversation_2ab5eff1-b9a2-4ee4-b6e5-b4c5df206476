import { useState } from 'react'

const Settings = ({ onClose, onClearAll, darkMode, onToggleDarkMode }) => {
  const [showConfirmClear, setShowConfirmClear] = useState(false)

  const handleClearAll = () => {
    onClearAll()
    setShowConfirmClear(false)
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            ⚙️ ตั้งค่า
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 text-xl"
          >
            ✕
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Dark Mode Toggle */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                โหมดมืด
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                เปลี่ยนธีมของแอปพลิเคชัน
              </p>
            </div>
            <button
              onClick={onToggleDarkMode}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                darkMode ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  darkMode ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* Clear All Data */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
            <div className="mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                ลบข้อมูลทั้งหมด
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                ลบงานทั้งหมดออกจากฐานข้อมูล (ไม่สามารถกู้คืนได้)
              </p>
            </div>

            {!showConfirmClear ? (
              <button
                onClick={() => setShowConfirmClear(true)}
                className="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
              >
                🗑️ ลบข้อมูลทั้งหมด
              </button>
            ) : (
              <div className="space-y-3">
                <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <p className="text-sm text-red-800 dark:text-red-200 font-medium">
                    ⚠️ คุณแน่ใจหรือไม่?
                  </p>
                  <p className="text-xs text-red-600 dark:text-red-300 mt-1">
                    การดำเนินการนี้จะลบงานทั้งหมดและไม่สามารถกู้คืนได้
                  </p>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={handleClearAll}
                    className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
                  >
                    ยืนยันลบ
                  </button>
                  <button
                    onClick={() => setShowConfirmClear(false)}
                    className="flex-1 px-4 py-2 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 rounded-lg font-medium transition-colors"
                  >
                    ยกเลิก
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* App Info */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              เกี่ยวกับแอป
            </h3>
            <div className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
              <p>📝 Todo PWA Application</p>
              <p>🚀 สร้างด้วย React + Vite + Tailwind CSS</p>
              <p>💾 ใช้ IndexedDB สำหรับจัดเก็บข้อมูล</p>
              <p>📱 รองรับการใช้งานแบบ Progressive Web App</p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-lg font-medium transition-colors"
          >
            ปิด
          </button>
        </div>
      </div>
    </div>
  )
}

export default Settings

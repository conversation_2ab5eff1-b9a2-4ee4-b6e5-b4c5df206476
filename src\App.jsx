import { useState, useEffect } from 'react'
import { todoService } from './db/database'
import TodoList from './components/TodoList'
import AddTodo from './components/AddTodo'
import FilterBar from './components/FilterBar'
import SearchBar from './components/SearchBar'
import Header from './components/Header'
import Settings from './components/Settings'

function App() {
  const [todos, setTodos] = useState([])
  const [filter, setFilter] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [darkMode, setDarkMode] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [loading, setLoading] = useState(true)

  // Load todos on component mount
  useEffect(() => {
    loadTodos()
  }, [])

  // Apply dark mode
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [darkMode])

  const loadTodos = async () => {
    try {
      setLoading(true)
      let loadedTodos = []

      if (searchTerm) {
        loadedTodos = await todoService.searchTodos(searchTerm)
      } else if (filter !== 'all') {
        loadedTodos = await todoService.getTodosByFilter(filter)
      } else {
        loadedTodos = await todoService.getAllTodos()
      }

      setTodos(loadedTodos)
    } catch (error) {
      console.error('Error loading todos:', error)
    } finally {
      setLoading(false)
    }
  }

  // Reload todos when filter or search changes
  useEffect(() => {
    loadTodos()
  }, [filter, searchTerm])

  const handleAddTodo = async (todoData) => {
    try {
      await todoService.addTodo(todoData)
      loadTodos()
    } catch (error) {
      console.error('Error adding todo:', error)
    }
  }

  const handleUpdateTodo = async (id, updates) => {
    try {
      await todoService.updateTodo(id, updates)
      loadTodos()
    } catch (error) {
      console.error('Error updating todo:', error)
    }
  }

  const handleDeleteTodo = async (id) => {
    try {
      await todoService.deleteTodo(id)
      loadTodos()
    } catch (error) {
      console.error('Error deleting todo:', error)
    }
  }

  const handleToggleTodo = async (id) => {
    try {
      await todoService.toggleTodo(id)
      loadTodos()
    } catch (error) {
      console.error('Error toggling todo:', error)
    }
  }

  const handleClearAll = async () => {
    try {
      await todoService.clearAllTodos()
      loadTodos()
    } catch (error) {
      console.error('Error clearing todos:', error)
    }
  }

  return (
    <div className={`min-h-screen transition-colors duration-200 ${
      darkMode ? 'dark bg-gray-900' : 'bg-gray-50'
    }`}>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Header
          darkMode={darkMode}
          onToggleDarkMode={() => setDarkMode(!darkMode)}
          onShowSettings={() => setShowSettings(true)}
        />

        <div className="space-y-6">
          <AddTodo onAddTodo={handleAddTodo} />

          <div className="flex flex-col sm:flex-row gap-4">
            <SearchBar
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
            />
            <FilterBar
              currentFilter={filter}
              onFilterChange={setFilter}
            />
          </div>

          <TodoList
            todos={todos}
            loading={loading}
            onUpdateTodo={handleUpdateTodo}
            onDeleteTodo={handleDeleteTodo}
            onToggleTodo={handleToggleTodo}
          />
        </div>

        {showSettings && (
          <Settings
            onClose={() => setShowSettings(false)}
            onClearAll={handleClearAll}
            darkMode={darkMode}
            onToggleDarkMode={() => setDarkMode(!darkMode)}
          />
        )}
      </div>
    </div>
  )
}

export default App

import { useState, useEffect } from 'react'
import { todoService } from '../db/database'

const Header = ({ darkMode, onToggleDarkMode, onShowSettings }) => {
  const [todosCount, setTodosCount] = useState({ total: 0, completed: 0, pending: 0 })

  useEffect(() => {
    loadTodosCount()
  }, [])

  const loadTodosCount = async () => {
    try {
      const count = await todoService.getTodosCount()
      setTodosCount(count)
    } catch (error) {
      console.error('Error loading todos count:', error)
    }
  }

  // Update count when todos change (we'll need to call this from parent)
  useEffect(() => {
    const interval = setInterval(loadTodosCount, 1000)
    return () => clearInterval(interval)
  }, [])

  return (
    <header className="mb-8">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            📝 Todo PWA
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            จัดการงานของคุณอย่างมีประสิทธิภาพ
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          {/* Stats */}
          <div className="hidden sm:flex items-center gap-4 text-sm">
            <div className="bg-blue-100 dark:bg-blue-900 px-3 py-1 rounded-full">
              <span className="text-blue-800 dark:text-blue-200">
                ทั้งหมด: {todosCount.total}
              </span>
            </div>
            <div className="bg-green-100 dark:bg-green-900 px-3 py-1 rounded-full">
              <span className="text-green-800 dark:text-green-200">
                เสร็จแล้ว: {todosCount.completed}
              </span>
            </div>
            <div className="bg-orange-100 dark:bg-orange-900 px-3 py-1 rounded-full">
              <span className="text-orange-800 dark:text-orange-200">
                รอดำเนินการ: {todosCount.pending}
              </span>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center gap-2">
            {/* Dark mode toggle */}
            <button
              onClick={onToggleDarkMode}
              className="p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              title={darkMode ? 'เปลี่ยนเป็นโหมดสว่าง' : 'เปลี่ยนเป็นโหมดมืด'}
            >
              {darkMode ? '☀️' : '🌙'}
            </button>

            {/* Settings button */}
            <button
              onClick={onShowSettings}
              className="p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              title="ตั้งค่า"
            >
              ⚙️
            </button>
          </div>
        </div>
      </div>

      {/* Mobile stats */}
      <div className="sm:hidden flex items-center gap-2 mt-4 text-sm">
        <div className="bg-blue-100 dark:bg-blue-900 px-3 py-1 rounded-full flex-1 text-center">
          <span className="text-blue-800 dark:text-blue-200">
            ทั้งหมด: {todosCount.total}
          </span>
        </div>
        <div className="bg-green-100 dark:bg-green-900 px-3 py-1 rounded-full flex-1 text-center">
          <span className="text-green-800 dark:text-green-200">
            เสร็จ: {todosCount.completed}
          </span>
        </div>
        <div className="bg-orange-100 dark:bg-orange-900 px-3 py-1 rounded-full flex-1 text-center">
          <span className="text-orange-800 dark:text-orange-200">
            รอ: {todosCount.pending}
          </span>
        </div>
      </div>
    </header>
  )
}

export default Header
